{"name": "New App", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/papaparse": "^5.3.15", "@uiw/react-heat-map": "^2.3.1", "@uiw/react-md-editor": "^4.0.4", "@uiw/react-textarea-code-editor": "^3.1.0", "framer-motion": "^11.13.4", "lucide-react": "^0.453.0", "nodemon": "^3.1.9", "papaparse": "^5.4.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.13.1", "react-markdown": "^9.0.1", "react-router-dom": "^7.0.2", "react-scroll": "^1.9.0", "react-slick": "^0.30.3", "rehype-highlight": "^7.0.1", "slick-carousel": "^1.8.1"}, "devDependencies": {"@types/react": "^18.3.6", "@types/react-dom": "^18.3.0", "@types/react-scroll": "^1.8.10", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "vite": "^5.4.6"}}