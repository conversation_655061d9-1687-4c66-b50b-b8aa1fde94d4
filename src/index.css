@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Arial, sans-serif;
  scroll-behavior: smooth;
  --toast-bg: #ffffff;
  --toast-color: #111827;
}

:root.dark {
  --toast-bg: rgba(31, 41, 55, 0.85);
  --toast-color: #F9FAFB;
}

/* Dark mode background setup */
.dark body {
  background-color: #1f2937; /* Solid dark gray */
  backdrop-filter: blur(12px);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}

/* Glass effect utilities directly defined */
.glass-effect {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(209, 213, 219, 0.2);
}

.dark .glass-effect {
  backdrop-filter: blur(12px);
  background-color: rgba(31, 41, 55, 0.75);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

/* Dark mode prose styles */
.dark .prose {
  color: #F9FAFB;
}

.dark .prose p {
  color: #D1D5DB;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3 {
  color: #F9FAFB;
}

.dark .prose code {
  background-color: rgba(55, 65, 81, 0.5);
  color: #F9FAFB;
}

.dark .prose blockquote {
  border-color: rgba(75, 85, 99, 0.5);
  color: #D1D5DB;
}

/* Base styles */
.dark .bg-white {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.dark .border-gray-100 {
  @apply border-gray-700/30;
}

.dark .shadow-sm {
  @apply shadow-gray-900/10;
}

.dark .text-gray-600 {
  @apply text-gray-300;
}

.dark .text-gray-900 {
  @apply text-gray-200; /* Lighter gray */
}

/* Dark mode input and form styles */
.dark input,
.dark textarea,
.dark select {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: #F3F4F6;
}

.dark input::placeholder,
.dark textarea::placeholder {
  @apply text-gray-400;
}

/* Dark mode button styles */
.dark .bg-gray-100 {
  background-color: rgba(31, 41, 55, 0.5);
  color: #D1D5DB;
  backdrop-filter: blur(12px);
}

.dark .hover\:bg-gray-200:hover {
  @apply hover:bg-blue-700/50;
}

/* Card and container background gradients */
.dark .bg-gradient-to-br {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Navbar specific styles */
.dark .navbar {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

/* Responsive container max-width */
.container {
  @apply max-w-7xl mx-auto;
}

/* Transition utilities */
.transition-colors {
  @apply transition-all duration-200;
}

/* Mobile-first responsive text */
h1 {
  @apply text-3xl sm:text-4xl lg:text-5xl;
}

h2 {
  @apply text-2xl sm:text-3xl lg:text-4xl;
}

h3 {
  @apply text-xl sm:text-2xl lg:text-3xl;
}

p {
  @apply text-base sm:text-lg;
}

/* Preserve scroll behavior */
html {
  scroll-behavior: smooth;
}

.scroll-mt-16 {
  scroll-margin-top: 4rem;
}
