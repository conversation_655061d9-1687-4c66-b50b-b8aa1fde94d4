@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Arial, sans-serif;
  scroll-behavior: smooth;
  --toast-bg: #ffffff;
  --toast-color: #111827;
}

:root.dark {
  --toast-bg: rgba(31, 41, 55, 0.85);
  --toast-color: #F9FAFB;
}

/* Dark mode background setup */
.dark body {
  background-color: #1f2937; /* Solid dark gray */
  backdrop-filter: blur(12px);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}

/* Glass effect utilities directly defined */
.glass-effect {
  backdrop-filter: blur(12px);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(209, 213, 219, 0.2);
}

.dark .glass-effect {
  backdrop-filter: blur(12px);
  background-color: rgba(31, 41, 55, 0.75);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Dark mode prose styles */
.dark .prose {
  color: #F9FAFB;
}

.dark .prose p {
  color: #D1D5DB;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3 {
  color: #F9FAFB;
}

.dark .prose code {
  background-color: rgba(55, 65, 81, 0.5);
  color: #F9FAFB;
}

.dark .prose blockquote {
  border-color: rgba(75, 85, 99, 0.5);
  color: #D1D5DB;
}

/* Base styles */
.dark .bg-white {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.dark .border-gray-100 {
  @apply border-gray-700/30;
}

.dark .shadow-sm {
  @apply shadow-gray-900/10;
}

.dark .text-gray-600 {
  @apply text-gray-300;
}

.dark .text-gray-900 {
  @apply text-gray-200; /* Lighter gray */
}

/* Dark mode input and form styles */
.dark input,
.dark textarea,
.dark select {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
  color: #F3F4F6;
}

.dark input::placeholder,
.dark textarea::placeholder {
  @apply text-gray-400;
}

/* Dark mode button styles */
.dark .bg-gray-100 {
  background-color: rgba(31, 41, 55, 0.5);
  color: #D1D5DB;
  backdrop-filter: blur(12px);
}

.dark .hover\:bg-gray-200:hover {
  @apply hover:bg-blue-700/50;
}

/* Card and container background gradients */
.dark .bg-gradient-to-br {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Navbar specific styles */
.dark .navbar {
  background-color: rgba(31, 41, 55, 0.75);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(75, 85, 99, 0.3);
}

/* Responsive container max-width */
.container {
  @apply max-w-7xl mx-auto;
}

/* Transition utilities */
.transition-colors {
  @apply transition-all duration-200;
}

/* Mobile-first responsive text */
h1 {
  @apply text-3xl sm:text-4xl lg:text-5xl;
}

h2 {
  @apply text-2xl sm:text-3xl lg:text-4xl;
}

h3 {
  @apply text-xl sm:text-2xl lg:text-3xl;
}

p {
  @apply text-base sm:text-lg;
}

/* Enhanced gradient backgrounds */
.gradient-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effects */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass-morphism {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced shadows */
.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

.shadow-glow-pink {
  box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Text effects */
.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Preserve scroll behavior */
html {
  scroll-behavior: smooth;
}

.scroll-mt-16 {
  scroll-margin-top: 4rem;
}

/* Line clamp utilities */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
