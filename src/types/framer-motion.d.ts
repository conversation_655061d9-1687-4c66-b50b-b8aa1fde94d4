declare module 'framer-motion' {
  export const motion: any;
  export const AnimatePresence: any;
  export const useAnimation: any;
  export const useCycle: any;
  export const usePresence: any;
  export const useReducedMotion: any;
  export const useSpring: any;
  export const useTransform: any;
  export const useViewportScroll: any;
  export const useInView: any;
  export const useMotionValue: any;
  export const useMotionTemplate: any;
  export const useTime: any;
  export const useVelocity: any;
  export const useWillChange: any;
  export const useDragControls: any;
  export const useHover: any;
  export const useTap: any;
  export const useFocus: any;
  export const usePan: any;
  export const useScroll: any;
  export const useElementScroll: any;
  export const useViewport: any;
  export const useDomEvent: any;
  export const useAnimate: any;
  export const useMotionValueEvent: any;
  export const useInvertedScale: any;
  export const useAnimatedState: any;
  export const useAnimatedValue: any;
  export const useAnimatedValues: any;
  export const useAnimatedStyle: any;
  export const useAnimatedProps: any;
  export const useAnimatedRef: any;
  export const useAnimatedScrollHandler: any;
  export const useAnimatedSensor: any;
  export const useAnimatedSensorEvent: any;
  export const useAnimatedSensorValue: any;
  export const useAnimatedSensorValues: any;
  export const useAnimatedSensorStyle: any;
  export const useAnimatedSensorProps: any;
  export const useAnimatedSensorRef: any;
  export const useAnimatedSensorScrollHandler: any;
}
