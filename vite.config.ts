import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    // Warm up transforms for common client files to reduce first-load latency
    warmup: {
      clientFiles: ['./index.html', './src/main.tsx', './src/App.tsx'],
    },
    fs: { strict: true },
    hmr: {
      overlay: false,
    },
  },
  optimizeDeps: {
    noDiscovery: true,
    entries: ['./src/main.tsx'],
    include: ['react', 'react-dom', 'react-router-dom', 'framer-motion'],
    esbuildOptions: {
      target: 'es2020',
    },
  },
  build: {
    chunkSizeWarningLimit: 600,
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': ['react', 'react-dom', 'react-router-dom', 'framer-motion'],
          'markdown': ['@uiw/react-md-editor'],
          'code-editor': ['@uiw/react-textarea-code-editor'],
        }
      }
    }
  },
  plugins: [react()],
  css: {
    devSourcemap: false,
  },
  esbuild: {
    sourcemap: false,
  },
});
